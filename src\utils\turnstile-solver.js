import { chromium, firefox } from 'playwright';
import { chromium as patchChromium } from 'patchright';
import { Camoufox } from 'camoufox-js';
import { info, warn, error, debug } from './logger.js';

/**
 * @typedef {Object} TurnstileResult
 * @property {string|null} turnstile_value - The solved turnstile token
 * @property {number} elapsed_time_seconds - Time taken to solve in seconds
 * @property {string} status - 'success' or 'failure'
 * @property {string} [reason] - Reason for failure (if status is 'failure')
 */

/**
 * ANSI color codes for console output
 */
const COLORS = {
  MAGENTA: '\x1b[35m',
  BLUE: '\x1b[34m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  RED: '\x1b[31m',
  RESET: '\x1b[0m'
};

/**
 * Custom logger class with colored output
 */
class CustomLogger {
  static formatMessage(level, color, message) {
    const timestamp = new Date().toLocaleTimeString();
    return `[${timestamp}] [${COLORS[color] || ''}${level}${COLORS.RESET}] -> ${message}`;
  }

  static debug(message) {
    debug(this.formatMessage('DEBUG', 'MAGENTA', message));
  }

  static info(message) {
    info(this.formatMessage('INFO', 'BLUE', message));
  }

  static success(message) {
    info(this.formatMessage('SUCCESS', 'GREEN', message));
  }

  static warning(message) {
    warn(this.formatMessage('WARNING', 'YELLOW', message));
  }

  static error(message) {
    error(this.formatMessage('ERROR', 'RED', message));
  }
}

/**
 * Async Turnstile Solver class
 */
class AsyncTurnstileSolver {
  /**
   * HTML template for the turnstile challenge page
   */
  static HTML_TEMPLATE = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Turnstile Solver</title>
        <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async></script>
        <script>
            async function fetchIP() {
                try {
                    const response = await fetch('https://api64.ipify.org?format=json');
                    const data = await response.json();
                    document.getElementById('ip-display').innerText = \`Your IP: \${data.ip}\`;
                } catch (error) {
                    console.error('Error fetching IP:', error);
                    document.getElementById('ip-display').innerText = 'Failed to fetch IP';
                }
            }
            window.onload = fetchIP;
        </script>
    </head>
    <body>
        <!-- cf turnstile -->
        <p id="ip-display">Fetching your IP...</p>
    </body>
    </html>
    `;

  /**
   * Constructor for AsyncTurnstileSolver
   * @param {Object} options - Configuration options
   * @param {boolean} [options.debug=false] - Enable debug logging
   * @param {boolean} [options.headless=false] - Run browser in headless mode
   * @param {string} [options.useragent] - Custom user agent string
   * @param {string} [options.browserType='chromium'] - Browser type to use
   */
  constructor(options = {}) {
    this.debug = options.debug || false;
    this.browserType = options.browserType || 'chromium';
    this.headless = options.headless !== undefined ? options.headless : false;
    this.useragent = options.useragent;
    this.browserArgs = [];
    
    if (this.useragent) {
      this.browserArgs.push(`--user-agent=${this.useragent}`);
    }
  }

  /**
   * Set up the page with Turnstile widget
   * @param {Object} browser - Browser instance
   * @param {string} url - Target URL
   * @param {string} sitekey - Turnstile site key
   * @param {string} [action] - Optional action parameter
   * @param {string} [cdata] - Optional cdata parameter
   * @returns {Promise<Object>} Page instance
   */
  async _setupPage(browser, url, sitekey, action = null, cdata = null) {
    let page;

    if (this.browserType === 'chrome') {
      page = browser.pages()[0];
    } else {
      page = await browser.newPage();
    }

    const urlWithSlash = url.endsWith('/') ? url : url + '/';

    // Build turnstile div with optional parameters
    let turnstileDiv = `<div class="cf-turnstile" data-sitekey="${sitekey}"`;
    if (action) turnstileDiv += ` data-action="${action}"`;
    if (cdata) turnstileDiv += ` data-cdata="${cdata}"`;
    turnstileDiv += '></div>';

    const pageData = AsyncTurnstileSolver.HTML_TEMPLATE.replace('<!-- cf turnstile -->', turnstileDiv);

    if (this.debug) {
      CustomLogger.debug(`Starting Turnstile solve for URL: ${url} with Sitekey: ${sitekey}`);
      CustomLogger.debug(`Turnstile div: ${turnstileDiv}`);
    }

    await page.route(urlWithSlash, route => route.fulfill({
      body: pageData,
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    }));

    await page.goto(urlWithSlash, { waitUntil: 'networkidle' });

    if (this.debug) {
      CustomLogger.debug('Page loaded, waiting for Turnstile script...');
    }

    // Wait for Turnstile script to load
    try {
      await page.waitForFunction(() => {
        return typeof window.turnstile !== 'undefined';
      }, { timeout: 15000 });

      if (this.debug) {
        CustomLogger.debug('Turnstile script loaded successfully');
      }
    } catch (err) {
      if (this.debug) {
        CustomLogger.warning('Turnstile script load timeout, continuing anyway...');
      }
    }

    // Wait a bit more for the widget to initialize
    await page.waitForTimeout(2000);

    return page;
  }

  /**
   * Attempt to retrieve Turnstile response
   * @param {Object} page - Page instance
   * @param {number} [maxAttempts=30] - Maximum number of attempts
   * @returns {Promise<string|null>} Turnstile token or null
   */
  async _getTurnstileResponse(page, maxAttempts = 30) {
    if (this.debug) {
      CustomLogger.debug('Waiting for Turnstile widget to load...');
    }

    // Wait for the Turnstile widget to appear
    try {
      await page.waitForSelector('.cf-turnstile', { timeout: 10000 });
      if (this.debug) {
        CustomLogger.debug('Turnstile widget found, waiting for challenge to complete...');
      }
    } catch (err) {
      if (this.debug) {
        CustomLogger.error('Turnstile widget not found within timeout');
      }
      return null;
    }

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      if (this.debug) {
        CustomLogger.debug(`Attempt ${attempt + 1}/${maxAttempts}: Checking for Turnstile response...`);
      }

      try {
        // Check if the response input exists
        const responseInput = await page.locator('[name="cf-turnstile-response"]');
        const inputExists = await responseInput.count() > 0;

        if (inputExists) {
          const turnstileValue = await responseInput.inputValue();

          if (this.debug) {
            CustomLogger.debug(`Turnstile response length: ${turnstileValue ? turnstileValue.length : 0}`);
          }

          if (turnstileValue && turnstileValue.length > 0) {
            if (this.debug) {
              CustomLogger.debug(`Found Turnstile response: ${turnstileValue.substring(0, 20)}...`);
            }
            return turnstileValue;
          }
        }

        // Try to click the Turnstile widget to trigger it
        try {
          const turnstileWidget = page.locator('.cf-turnstile');
          const widgetCount = await turnstileWidget.count();

          if (widgetCount > 0) {
            if (this.debug) {
              CustomLogger.debug('Clicking Turnstile widget to trigger challenge...');
            }
            await turnstileWidget.first().click({ timeout: 3000 });
          }
        } catch (clickErr) {
          if (this.debug) {
            CustomLogger.debug(`Click attempt failed: ${clickErr.message}`);
          }
        }

        // Wait before next attempt
        await page.waitForTimeout(1000);

      } catch (err) {
        if (this.debug) {
          CustomLogger.debug(`Attempt ${attempt + 1} error: ${err.message}`);
        }
        await page.waitForTimeout(500);
      }
    }

    if (this.debug) {
      CustomLogger.error(`Failed to get Turnstile response after ${maxAttempts} attempts`);
    }
    return null;
  }

  /**
   * Launch browser based on browser type
   * @returns {Promise<Object>} Browser instance
   */
  async _launchBrowser() {
    let browser = null;
    let playwrightInstance = null;

    if (this.debug) {
      CustomLogger.debug(`Launching browser type: ${this.browserType}`);
    }

    if (['chromium', 'chrome', 'msedge'].includes(this.browserType)) {
      browser = await chromium.launch({
        headless: this.headless,
        args: this.browserArgs
      });
      if (this.debug) {
        CustomLogger.debug('Chromium browser launched successfully');
      }
    } else if (this.browserType === 'patchright') {
      browser = await patchChromium.launch({
        headless: this.headless,
        args: this.browserArgs
      });
      if (this.debug) {
        CustomLogger.debug('Patchright browser launched successfully');
      }
    } else if (this.browserType === 'firefox') {
      browser = await firefox.launch({
        headless: this.headless,
        args: this.browserArgs
      });
      if (this.debug) {
        CustomLogger.debug('Firefox browser launched successfully');
      }
    } else if (this.browserType === 'camoufox') {
      browser = await Camoufox({
        headless: this.headless,
        args: this.browserArgs
      });
      if (this.debug) {
        CustomLogger.debug('Camoufox browser launched successfully');
      }
    } else {
      throw new Error(`Unsupported browser type: ${this.browserType}`);
    }

    return { browser, playwright: playwrightInstance };
  }

  /**
   * Close browser and cleanup
   * @param {Object} browser - Browser instance
   * @param {Object} [playwright] - Playwright instance
   */
  async _closeBrowser(browser, playwright = null) {
    if (this.debug) {
      CustomLogger.debug('Closing browser...');
    }

    try {
      if (browser) {
        if (this.browserType === 'camoufox') {
          // Camoufox has a different close method
          if (typeof browser.stop === 'function') {
            await browser.stop();
          } else {
            await browser.close();
          }
        } else {
          await browser.close();
        }

        if (this.debug) {
          CustomLogger.debug('Browser closed successfully');
        }
      }
    } catch (err) {
      if (this.debug) {
        CustomLogger.warning(`Error closing browser: ${err.message}`);
      }
    }

    if (playwright) {
      try {
        await playwright.stop();
        if (this.debug) {
          CustomLogger.debug('Playwright stopped successfully');
        }
      } catch (err) {
        if (this.debug) {
          CustomLogger.warning(`Error stopping playwright: ${err.message}`);
        }
      }
    }
  }

  /**
   * Solve the Turnstile challenge and return the result
   * @param {string} url - Target URL
   * @param {string} sitekey - Turnstile site key
   * @param {string} [action] - Optional action parameter
   * @param {string} [cdata] - Optional cdata parameter
   * @returns {Promise<TurnstileResult>} Result object
   */
  async solve(url, sitekey, action = null, cdata = null) {
    const startTime = Date.now();
    let browser = null;
    let playwright = null;

    try {
      const browserResult = await this._launchBrowser();
      browser = browserResult.browser;
      playwright = browserResult.playwright;

      const page = await this._setupPage(browser, url, sitekey, action, cdata);
      const turnstileValue = await this._getTurnstileResponse(page);

      const elapsedTime = Math.round((Date.now() - startTime) / 1000 * 1000) / 1000;

      if (!turnstileValue) {
        CustomLogger.error('Failed to retrieve Turnstile value.');
        return {
          turnstile_value: null,
          elapsed_time_seconds: elapsedTime,
          status: 'failure',
          reason: 'Max attempts reached without token retrieval'
        };
      } else {
        CustomLogger.success(`Successfully solved captcha: ${turnstileValue.substring(0, 45)}... in ${elapsedTime} seconds`);
        return {
          turnstile_value: turnstileValue,
          elapsed_time_seconds: elapsedTime,
          status: 'success'
        };
      }
    } finally {
      await this._closeBrowser(browser, playwright);
      
      if (this.debug) {
        const elapsedTime = Math.round((Date.now() - startTime) / 1000 * 1000) / 1000;
        CustomLogger.debug(`Elapsed time: ${elapsedTime} seconds`);
        CustomLogger.debug('Browser closed. Returning result.');
      }
    }
  }
}

/**
 * Legacy wrapper function for backward compatibility
 * @param {string} url - Target URL
 * @param {string} sitekey - Turnstile site key
 * @param {string} [action] - Optional action parameter
 * @param {string} [cdata] - Optional cdata parameter
 * @param {boolean} [debug=false] - Enable debug logging
 * @param {boolean} [headless=false] - Run browser in headless mode
 * @param {string} [useragent] - Custom user agent string
 * @param {string} [browserType='chromium'] - Browser type to use
 * @returns {Promise<Object>} Result object
 */
export async function getTurnstileToken(url, sitekey, action = null, cdata = null, debug = false, headless = false, useragent = null, browserType = 'chromium') {
  const browserTypes = ['chromium', 'chrome', 'camoufox', 'msedge'];
  
  if (!browserTypes.includes(browserType)) {
    CustomLogger.error(`Unknown browser type: ${COLORS.RED}${browserType}${COLORS.RESET} Available browser types: ${browserTypes.join(', ')}`);
    return {
      turnstile_value: null,
      elapsed_time_seconds: 0,
      status: 'failure',
      reason: `Unknown browser type: ${browserType}`
    };
  }
  
  if (headless === true && useragent === null && !browserType.includes('camoufox')) {
    CustomLogger.error(`You must specify a ${COLORS.YELLOW}User-Agent${COLORS.RESET} for Turnstile Solver or use ${COLORS.GREEN}camoufox${COLORS.RESET} without useragent`);
    return {
      turnstile_value: null,
      elapsed_time_seconds: 0,
      status: 'failure',
      reason: 'User-Agent required for headless mode with this browser type'
    };
  }

  const solver = new AsyncTurnstileSolver({
    debug,
    useragent,
    headless,
    browserType
  });
  
  return await solver.solve(url, sitekey, action, cdata);
}

export { AsyncTurnstileSolver };
