import { getTurnstileToken, AsyncTurnstileSolver } from './turnstile-solver.js';

/**
 * Test function to demonstrate Turnstile solver usage
 */
async function testTurnstileSolver() {
  console.log('🚀 Testing Turnstile Solver...\n');

  try {
    // Example 1: Simple test with Cloudflare demo site
    console.log('📝 Example 1: Testing with Cloudflare demo site');
    const result1 = await getTurnstileToken(
      'https://demo.cloudflare.com',
      '1x00000000000000000000AA', // Demo site key
      null, // action
      null, // cdata
      true, // debug
      false, // headless
      null, // useragent
      'chromium' // browserType
    );

    console.log('Result 1:', result1);
    console.log('');

    // Example 2: Using the class directly with a simple test
    console.log('📝 Example 2: Using AsyncTurnstileSolver class');
    const solver = new AsyncTurnstileSolver({
      debug: true,
      headless: false,
      browserType: 'chromium'
    });

    const result2 = await solver.solve(
      'https://grok.com/',
      '0x4AAAAAAADnPIDROrmt1Wwj' // Demo site key
    );

    console.log('Result 2:', result2);
    console.log('');

    // Example 3: Test with headless mode
    console.log('📝 Example 3: Testing headless mode');
    const result3 = await getTurnstileToken(
      'https://demo.cloudflare.com',
      '1x00000000000000000000AA',
      null, // action
      null, // cdata
      true, // debug
      true, // headless
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', // useragent
      'chromium' // browserType
    );

    console.log('Result 3:', result3);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

/**
 * Example usage with different browser types
 */
async function testDifferentBrowsers() {
  console.log('🌐 Testing different browser types...\n');

  const testCases = [
    { browserType: 'chromium', name: 'Chromium' },
    { browserType: 'camoufox', name: 'Camoufox (Firefox-based)' },
    // Add more browser types as needed
  ];

  for (const testCase of testCases) {
    try {
      console.log(`📝 Testing with ${testCase.name}...`);
      
      const result = await getTurnstileToken(
        'https://example.com',
        'test-site-key',
        null,
        null,
        false, // debug off for cleaner output
        true, // headless
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        testCase.browserType
      );
      
      console.log(`✅ ${testCase.name} result:`, result.status);
      
    } catch (error) {
      console.error(`❌ ${testCase.name} failed:`, error.message);
    }
    
    console.log('');
  }
}

// Run tests if this file is executed directly

  console.log('🧪 Turnstile Solver Test Suite\n');
  
  // Uncomment the test you want to run:
  
  // Basic functionality test
   await testTurnstileSolver();
  
  // Browser compatibility test
  // await testDifferentBrowsers();
  
  console.log('ℹ️  Uncomment the test functions above to run them.');
  console.log('ℹ️  Make sure to replace site keys with real ones for actual testing.');


export { testTurnstileSolver, testDifferentBrowsers };
