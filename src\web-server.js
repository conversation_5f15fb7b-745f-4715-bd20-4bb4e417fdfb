import { H3, serve, serveStatic } from 'h3';
import { stat, readFile } from 'node:fs/promises';
import { join } from 'node:path';
import {
  initScript,
  setupProcessHandlers,
  initPagePool,
  getPageFromPool,
  getBrowserEnvironmentManager,
  releasePageToPool,
  handlePageInitialBlocks,
  smartNavigate
} from './utils/browser.js';
import { startPagePoolMonitoring, printPagePoolStatus } from './utils/page-pool-monitor.js';
import { corsMiddleware } from './middlewares/cors.js';
import { healthHandler } from './routes/health.js';
import { chatCompletionsHandler } from './routes/chat-completions.js';
import { modelsHandler, modelHandler } from './routes/models.js';
import { saveScreenshot } from './utils/common-utils.js';
import config from './config.js';
import { info, error } from './utils/logger.js';

const app = new H3();
const CWD = process.cwd();

// 设置进程退出处理
setupProcessHandlers();

// 全局 CORS 中间件
app.use(corsMiddleware);

// --- API 端点 ---
// 健康检查端点
app.get('/health', healthHandler);

// 模型相关端点
app.get('/v1/models', modelsHandler);
app.get('/v1/models/:model', modelHandler);

// 主要的聊天完成端点
app.post('/v1/chat/completions', chatCompletionsHandler);

// --- 静态文件服务 (必须在 API 端点之后) ---
// 处理 screenshots 目录
app.get('/screenshots/**', (event) => {
  // event.path is like /screenshots/foo.png
  const assetPath = event.path.substring('/screenshots'.length);
  const filePath = join(CWD, 'screenshots', assetPath);
  return serveStatic(event, {
    getContents: () => readFile(filePath),
    getMeta: async () => {
      const stats = await stat(filePath).catch(() => { });
      if (stats?.isFile()) return { size: stats.size, mtime: stats.mtimeMs };
    }
  });
});

// 处理 public 目录 (作为其他所有 GET 请求的兜底)
app.get('/**', (event) => {
  const assetPath = event.path === '/' ? 'index.html' : event.path;
  const filePath = join(CWD, 'public', assetPath);
  return serveStatic(event, {
    getContents: () => readFile(filePath),
    getMeta: async () => {
      const stats = await stat(filePath).catch(() => { });
      if (stats?.isFile()) return { size: stats.size, mtime: stats.mtimeMs };
    }
  });
});


// 初始化页面池
info('🔧 初始化页面池...');
initPagePool(5); // 最大5个页面

// 启动页面池监控（开发环境）
if (process.env.NODE_ENV !== 'production') {
  info('📊 启动页面池监控...');
  startPagePoolMonitoring(10000); // 每10秒监控一次
}

// 启动服务器
const port = config.server.port;
const host = config.server.host;
serve(app, { port, host });

info(`🚀 服务器运行在 http://${host}:${port}`);
info(`🏠 管理面板: http://${host}:${port}/`);
info(`📋 健康检查: http://${host}:${port}/health`);
info(`💬 聊天端点: http://${host}:${port}/v1/chat/completions`);
info(`🎯 App URL: ${config.app.url}`);
info(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);

// 截图App页面的函数
async function captureScreenshot() {
  let page;
  try {
    if (config.app.useAppToken) {
      return;
    }

    info('📸 开始获取App页面截图...');
    page = await getPageFromPool(null); // 使用默认 context
    await initScript(page); // 初始化脚本
    info(`智能导航到: ${config.app.url}`);
    await smartNavigate(page, config.app.url, {
      timeout: config.app.pageTimeout
    });

    await handlePageInitialBlocks(page);
    await page.waitForTimeout(2000);

    const screenshotPath = await saveScreenshot(page, './screenshots', 'app-startup', false);
    info(`✅ App截图已保存: ${screenshotPath}`);

  } catch (err) {
    error('❌ 截图App页面时出错:', err.message);
  } finally {
    if (page) {
      await releasePageToPool(page, null); // 使用默认 context
      info('🔄 页面已释放回池中');
    }
  }
}

// 打印初始页面池状态
setTimeout(() => {
  info('\n📊 初始页面池状态:');
  printPagePoolStatus();
}, 1000);

// 启动后截图App页面
setTimeout(() => {
  captureScreenshot();
}, 2000);