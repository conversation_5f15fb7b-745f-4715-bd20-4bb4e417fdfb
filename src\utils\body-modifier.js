/**
 * 修改请求body中的模型ID
 * @param {string} bodyStr 原始body字符串
 * @param {string} model 目标模型名称
 * @returns {string} 修改后的body字符串
 */
export function modifyBodyForModel(bodyStr, model) {
  try {
    const body = JSON.parse(bodyStr);

    // 替换 modelName
    if (body.modelName) {
      body.modelName = model;
    }

    // 替换 responseMetadata.requestModelDetails.modelId
    if (body.responseMetadata && body.responseMetadata.requestModelDetails && body.responseMetadata.requestModelDetails.modelId) {
      body.responseMetadata.requestModelDetails.modelId = model;
    }

    return JSON.stringify(body);
  } catch (err) {
    console.error('[modifyBodyForModel] JSON解析失败:', err);
    return bodyStr;
  }
}
